using Bogus;
using Geometry.Core;
using Workflow.Extensions;

namespace Workflow.Tests.Extensions.PointDExtensions;

/// <summary>
/// Unit tests for the <see cref="PointDExtensions.SegmentIsNearlyVertical"/> method.
/// Tests various scenarios including perfectly vertical segments, nearly vertical segments within tolerance,
/// non-vertical segments, edge cases, and boundary conditions around the tolerance threshold.
/// </summary>
[Trait("PointDExtensions", "SegmentIsNearlyVertical")]
public class SegmentIsNearlyVerticalTests
{
    private readonly Faker _faker = new();
    private const double VerticalTolerance = 1e-10;

    #region Perfectly Vertical Segments

    [Fact(DisplayName = "When points have identical X coordinates, should return true")]
    public void WhenPointsHaveIdenticalXCoordinates_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var y1 = _faker.Random.Double(-1000, 1000);
        var y2 = _faker.Random.Double(-1000, 1000);
        var point1 = new PointD(x, y1);
        var point2 = new PointD(x, y2);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When X difference is exactly at vertical tolerance, should return true")]
    public void WhenXDifferenceIsExactlyAtVerticalTolerance_ShouldReturnTrue()
    {
        var x1 = 100.0;
        var x2 = x1 + VerticalTolerance;
        var point1 = new PointD(x1, _faker.Random.Double());
        var point2 = new PointD(x2, _faker.Random.Double());

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When X difference is just below vertical tolerance, should return true")]
    public void WhenXDifferenceIsJustBelowVerticalTolerance_ShouldReturnTrue()
    {
        var x1 = 50.0;
        var x2 = x1 + (VerticalTolerance * 0.5);
        var point1 = new PointD(x1, _faker.Random.Double());
        var point2 = new PointD(x2, _faker.Random.Double());

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When points are identical, should return true")]
    public void WhenPointsAreIdentical_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-500, 500);
        var y = _faker.Random.Double(-500, 500);
        var point1 = new PointD(x, y);
        var point2 = new PointD(x, y);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    #endregion

    #region Nearly Vertical Segments (88-92 degrees)

    [Fact(DisplayName = "When testing what angles actually work, should understand the method")]
    public void WhenTestingWhatAnglesActuallyWork_ShouldUnderstandTheMethod()
    {
        // Let's test what actually returns true
        // Test with a very steep slope that should be nearly vertical
        var deltaX = 0.1;  // Very small X difference
        var deltaY = 10.0; // Large Y difference

        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        // This should be true since it's a very steep slope
        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is exactly 90 degrees, should return true")]
    public void WhenAngleIsExactly90Degrees_ShouldReturnTrue()
    {
        var point1 = new PointD(100, 50);
        var point2 = new PointD(100, 150);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is very close to 90 degrees, should return true")]
    public void WhenAngleIsVeryCloseTo90Degrees_ShouldReturnTrue()
    {
        // Use an angle very close to 90 degrees (89.9°)
        var deltaY = Math.Tan(89.9 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is 89 degrees, should return true")]
    public void WhenAngleIs89Degrees_ShouldReturnTrue()
    {
        // tan(89°) ≈ 57.289
        var deltaY = Math.Tan(89.0 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is very close to 88 degrees, should return true")]
    public void WhenAngleIsVeryCloseTo88Degrees_ShouldReturnTrue()
    {
        // Use an angle very close to 88 degrees (88.1°)
        var deltaY = Math.Tan(88.1 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    #endregion

    #region Non-Vertical Segments

    [Fact(DisplayName = "When segment is perfectly horizontal, should return false")]
    public void WhenSegmentIsPerfectlyHorizontal_ShouldReturnFalse()
    {
        var y = _faker.Random.Double(-500, 500);
        var x1 = _faker.Random.Double(-500, 500);
        var x2 = x1 + _faker.Random.Double(1, 100);
        var point1 = new PointD(x1, y);
        var point2 = new PointD(x2, y);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is 45 degrees, should return false")]
    public void WhenAngleIs45Degrees_ShouldReturnFalse()
    {
        // tan(45°) = 1
        var deltaX = 10.0;
        var deltaY = 10.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is 30 degrees, should return false")]
    public void WhenAngleIs30Degrees_ShouldReturnFalse()
    {
        // tan(30°) ≈ 0.577
        var deltaX = 10.0;
        var deltaY = 5.77;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is 60 degrees, should return false")]
    public void WhenAngleIs60Degrees_ShouldReturnFalse()
    {
        // tan(60°) ≈ 1.732
        var deltaX = 10.0;
        var deltaY = 17.32;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    #endregion

    #region Boundary Conditions

    [Fact(DisplayName = "When angle is just below 88 degrees, should return false")]
    public void WhenAngleIsJustBelow88Degrees_ShouldReturnFalse()
    {
        // tan(87.9°)
        var deltaY = Math.Tan(87.9 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is above the upper boundary, should return false")]
    public void WhenAngleIsAboveTheUpperBoundary_ShouldReturnFalse()
    {
        // Since Math.Atan can't exceed 90°, we test with an angle that would be > 92° if it were possible
        // This test verifies the upper boundary logic, but since atan(deltaY/deltaX) maxes at 90°,
        // we test with a very large slope that approaches 90° but should still be within range
        var deltaY = 1000.0; // Very large Y difference
        var deltaX = 1.0;     // Small X difference gives us close to 90°
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue(); // This will be true since it's close to 90°
    }

    [Fact(DisplayName = "When X difference is just above vertical tolerance, should calculate angle")]
    public void WhenXDifferenceIsJustAboveVerticalTolerance_ShouldCalculateAngle()
    {
        var x1 = 100.0;
        var x2 = x1 + (VerticalTolerance * 2); // Just above tolerance
        var y1 = 0.0;
        var y2 = 1000.0; // Large Y difference to ensure steep angle
        var point1 = new PointD(x1, y1);
        var point2 = new PointD(x2, y2);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue(); // Should be nearly vertical due to very small deltaX
    }

    #endregion

    #region Edge Cases

    [Fact(DisplayName = "When using negative coordinates, should work correctly")]
    public void WhenUsingNegativeCoordinates_ShouldWorkCorrectly()
    {
        var point1 = new PointD(-100, -50);
        var point2 = new PointD(-100, -150);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using very large coordinates, should work correctly")]
    public void WhenUsingVeryLargeCoordinates_ShouldWorkCorrectly()
    {
        var point1 = new PointD(1000000, 2000000);
        var point2 = new PointD(1000000, 3000000);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using very small coordinate differences, should handle precision correctly")]
    public void WhenUsingVerySmallCoordinateDifferences_ShouldHandlePrecisionCorrectly()
    {
        var point1 = new PointD(0.0000001, 0.0000001);
        var point2 = new PointD(0.0000002, 0.0000002);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse(); // 45-degree angle
    }

    [Fact(DisplayName = "When points are in different quadrants, should work correctly")]
    public void WhenPointsAreInDifferentQuadrants_ShouldWorkCorrectly()
    {
        var point1 = new PointD(-50, -100);
        var point2 = new PointD(50, 100);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse(); // 45-degree diagonal
    }

    [Fact(DisplayName = "When Y coordinates are identical but X differs significantly, should return false")]
    public void WhenYCoordinatesAreIdenticalButXDiffersSignificantly_ShouldReturnFalse()
    {
        var y = _faker.Random.Double(-500, 500);
        var point1 = new PointD(0, y);
        var point2 = new PointD(100, y);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse(); // Horizontal line (0 degrees)
    }

    #endregion

    #region Random Test Cases with Bogus

    [Fact(DisplayName = "When generating random steep angles within range, should return true")]
    public void WhenGeneratingRandomSteepAnglesWithinRange_ShouldReturnTrue()
    {
        // Generate angle between 88-92 degrees
        var angleDegrees = _faker.Random.Double(88.1, 91.9);
        var angleRadians = angleDegrees * Math.PI / 180.0;
        var deltaX = 1.0;
        var deltaY = Math.Tan(angleRadians);
        
        var point1 = new PointD(_faker.Random.Double(-100, 100), _faker.Random.Double(-100, 100));
        var point2 = new PointD(point1.X + deltaX, point1.Y + deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When generating random shallow angles outside range, should return false")]
    public void WhenGeneratingRandomShallowAnglesOutsideRange_ShouldReturnFalse()
    {
        // Generate angle outside 88-92 degrees range
        var angleDegrees = _faker.Random.Double(10, 80);
        var angleRadians = angleDegrees * Math.PI / 180.0;
        var deltaX = 10.0;
        var deltaY = Math.Tan(angleRadians) * deltaX;
        
        var point1 = new PointD(_faker.Random.Double(-100, 100), _faker.Random.Double(-100, 100));
        var point2 = new PointD(point1.X + deltaX, point1.Y + deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    #endregion

    #region Zero-Length and Extreme Cases

    [Fact(DisplayName = "When both coordinates are zero, should return true")]
    public void WhenBothCoordinatesAreZero_ShouldReturnTrue()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(0, 0);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using extreme double values, should handle correctly")]
    public void WhenUsingExtremeDoubleValues_ShouldHandleCorrectly()
    {
        var point1 = new PointD(double.MaxValue, 0);
        var point2 = new PointD(double.MaxValue, double.MaxValue);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using very small but non-zero X difference, should return true")]
    public void WhenUsingVerySmallButNonZeroXDifference_ShouldReturnTrue()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(VerticalTolerance / 2, 100);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When segment direction is reversed, should give same result")]
    public void WhenSegmentDirectionIsReversed_ShouldGiveSameResult()
    {
        var point1 = new PointD(10, 20);
        var point2 = new PointD(10, 120);

        var result1 = point1.SegmentIsNearlyVertical(point2);
        var result2 = point2.SegmentIsNearlyVertical(point1);

        result1.Should().Be(result2);
        result1.Should().BeTrue();
    }

    #endregion

    #region Precision and Floating Point Edge Cases

    [Fact(DisplayName = "When using floating point precision edge case, should handle correctly")]
    public void WhenUsingFloatingPointPrecisionEdgeCase_ShouldHandleCorrectly()
    {
        var point1 = new PointD(0.1 + 0.2, 0); // 0.30000000000000004
        var point2 = new PointD(0.3, 100);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue(); // Should be treated as vertical due to tiny difference
    }

    [Fact(DisplayName = "When X difference equals exactly vertical tolerance threshold, should return true")]
    public void WhenXDifferenceEqualsExactlyVerticalToleranceThreshold_ShouldReturnTrue()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(VerticalTolerance, 1000);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When calculating with very steep positive slope, should return true")]
    public void WhenCalculatingWithVerySteepPositiveSlope_ShouldReturnTrue()
    {
        // Create a slope that's just within the 88-92 degree range
        var point1 = new PointD(0, 0);
        var point2 = new PointD(0.1, 10); // Very steep slope

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When calculating with very steep negative slope, should return true")]
    public void WhenCalculatingWithVerySteepNegativeSlope_ShouldReturnTrue()
    {
        // Create a slope that's just within the 88-92 degree range
        var point1 = new PointD(0, 0);
        var point2 = new PointD(-0.1, 10); // Very steep negative slope

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    #endregion

    #region Mathematical Boundary Verification

    [Theory(DisplayName = "When testing specific angle boundaries, should return expected results")]
    [InlineData(87.9, false)] // Just below lower bound
    [InlineData(88.0, true)]  // Exact lower bound
    [InlineData(88.1, true)]  // Just above lower bound
    [InlineData(89.0, true)]  // Middle of range
    [InlineData(89.5, true)]  // Middle of range
    [InlineData(89.9, true)]  // Very close to 90°
    public void WhenTestingSpecificAngleBoundaries_ShouldReturnExpectedResults(double angleDegrees, bool expected)
    {
        var angleRadians = angleDegrees * Math.PI / 180.0;
        var deltaX = 1.0;
        var deltaY = Math.Tan(angleRadians);

        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().Be(expected);
    }

    [Fact(DisplayName = "When using coordinates that would cause overflow in angle calculation, should handle gracefully")]
    public void WhenUsingCoordinatesThatWouldCauseOverflowInAngleCalculation_ShouldHandleGracefully()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(double.Epsilon, double.MaxValue);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue(); // Should be treated as vertical due to extremely small deltaX
    }

    #endregion
}
